"""
Unified Helper System for Task Processing Across Services

This module provides a consolidated helper system that eliminates duplicate
functionality across socket service, editor service, and followup service.

Key Components:
- TaskProcessor: Main orchestrator for task processing workflows
- MediaManager: Unified media generation with caching
- DatabaseManager: Abstracted database operations
- ConfigManager: Service-specific configuration management

Usage:
    from app.shared.helpers import TaskProcessor
    
    processor = TaskProcessor(service_name="socket_service_v2")
    await processor.process_task_set(current_user, task_set_id, tasks)
"""

from app.shared.helpers.core.task_processor import TaskProcessor
from app.shared.helpers.core.config_manager import ConfigManager
from app.shared.helpers.media.media_manager import MediaManager
from app.shared.helpers.database.db_manager import DatabaseManager
from app.shared.helpers.database.status_manager import StatusManager
from app.shared.helpers.storage.storage_manager import StorageManager

__all__ = [
    "TaskProcessor",
    "ConfigManager",
    "MediaManager",
    "DatabaseManager",
    "StatusManager",
    "StorageManager"
]
