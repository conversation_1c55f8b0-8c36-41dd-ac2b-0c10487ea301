"""
Audio Generator - Unified Audio Generation Across Services

Consolidates audio generation functionality from different services
with support for multiple TTS engines and service-specific configurations.
"""

from typing import Dict, Any, Optional, Union, List
from datetime import datetime, timezone

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.helpers.media.cache_manager import CacheManager

logger = setup_new_logging(__name__)


class AudioGenerator:
    """
    Unified audio generation with multiple TTS engine support.
    
    Consolidates audio generation from socket service, editor service,
    and followup service with intelligent caching and optimization.
    """
    
    def __init__(self, service_name: str, config_manager, cache_manager: CacheManager):
        """
        Initialize audio generator.
        
        Args:
            service_name: Name of the service
            config_manager: Configuration manager instance
            cache_manager: Cache manager instance
        """
        self.service_name = service_name
        self.config_manager = config_manager
        self.cache_manager = cache_manager
        self.current_user = None
        self.audio_engine = None
        self.audio_params = None
    
    async def initialize(self, current_user: UserTenantDB):
        """
        Initialize with user context and service configuration.
        
        Args:
            current_user: User context for database operations
        """
        self.current_user = current_user
        
        # Get service-specific audio configuration
        config = self.config_manager.get_config(self.service_name)
        self.audio_engine = config.media["audio_engine"]
        self.audio_params = config.media["audio_params"]
        
        logger.info(f"AudioGenerator initialized for {self.service_name} with {self.audio_engine}")
    
    async def generate_audio(
        self,
        keyword: Union[str, List[str]],
        audio_type: str = "audio_prompt",
        cache_enabled: bool = True
    ) -> tuple[Optional[str], Optional[Dict[str, Any]], Dict[str, Any]]:
        """
        Generate audio with unified interface.
        
        Args:
            keyword: Text to convert to audio (string or list)
            audio_type: Type of audio generation
            cache_enabled: Whether to use caching
            
        Returns:
            tuple: (file_text, file_info, usage_metadata)
        """
        try:
            if not self.current_user:
                raise ValueError("AudioGenerator not initialized with user context")
            
            # Handle list input
            if isinstance(keyword, list):
                keyword = ", ".join(keyword)
            
            logger.info(f"Generating audio for keyword: {keyword} using {self.audio_engine}")
            
            # Check cache first if enabled
            if cache_enabled:
                cached_audio = await self.cache_manager.check_media_cache(
                    keyword, "audio", audio_type
                )
                if cached_audio:
                    logger.info(f"Using cached audio for keyword: {keyword}")
                    return (
                        cached_audio["file_text"],
                        cached_audio["file_info"],
                        cached_audio["usage_metadata"]
                    )
            
            # Generate new audio based on engine
            if self.audio_engine == "pipertts":
                file_bytes, usage_metadata = await self._generate_pipertts_audio(keyword)
            elif self.audio_engine == "edgetts":
                file_bytes, usage_metadata = await self._generate_edgetts_audio(keyword)
            else:
                raise ValueError(f"Unsupported audio engine: {self.audio_engine}")
            
            if not file_bytes:
                logger.error(f"No audio data received for: {keyword}")
                return None, None, usage_metadata
            
            # Store the audio file
            file_info = await self._store_audio_file(keyword, file_bytes, audio_type)
            if not file_info:
                return None, None, usage_metadata
            
            # Cache the result if enabled
            if cache_enabled:
                await self.cache_manager.store_media_cache(
                    keyword, "audio", file_info.get("object_path", ""), 
                    file_info, usage_metadata, audio_type
                )
            
            return file_info.get("object_path", ""), file_info, usage_metadata
            
        except Exception as e:
            logger.error(f"Audio generation failed for '{keyword}': {e}")
            return None, None, {}
    
    async def _generate_pipertts_audio(self, text: str) -> tuple[Optional[bytes], Dict[str, Any]]:
        """Generate audio using PiperTTS."""
        try:
            # Import PiperTTS function
            from app.shared.tts.pipertts import generate_audio_bytes as generate_audio_bytes_pipertts
            
            file_bytes = await generate_audio_bytes_pipertts(
                text=text,
                gender=self.audio_params.get("gender", "ne-female-400"),
                speaker=self.audio_params.get("speaker", 0),
                length_scale=self.audio_params.get("length_scale", 1.15),
                noise_scale=self.audio_params.get("noise_scale", 0.667),
                noise_w=self.audio_params.get("noise_w", 0.8),
                sentence_silence=self.audio_params.get("sentence_silence", 0.5)
            )
            
            usage_metadata = {
                "engine": "pipertts",
                "text_length": len(text),
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
            
            return file_bytes, usage_metadata
            
        except Exception as e:
            logger.error(f"PiperTTS generation failed: {e}")
            return None, {}
    
    async def _generate_edgetts_audio(self, text: str) -> tuple[Optional[bytes], Dict[str, Any]]:
        """Generate audio using EdgeTTS."""
        try:
            # Import EdgeTTS function
            from app.shared.tts.edgetts import generate_audio_bytes as generate_audio_bytes_edgetts
            
            file_bytes = await generate_audio_bytes_edgetts(
                text=text,
                language=self.audio_params.get("language", "ne"),
                gender=self.audio_params.get("gender", "Female")
            )
            
            usage_metadata = {
                "engine": "edgetts",
                "text_length": len(text),
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
            
            return file_bytes, usage_metadata
            
        except Exception as e:
            logger.error(f"EdgeTTS generation failed: {e}")
            return None, {}
    
    async def _store_audio_file(
        self,
        keyword: str,
        file_bytes: bytes,
        audio_type: str
    ) -> Optional[Dict[str, Any]]:
        """Store audio file in Minio storage."""
        try:
            if not file_bytes:
                return None
            
            # Generate filename
            safe_keyword = "".join(c for c in keyword if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_keyword}_{audio_type}.wav"
            
            # Store in Minio
            file_info = self.current_user.minio_client.save_file(
                data=file_bytes,
                user_id=self.current_user.user_id,
                content_type="audio/wav",
                folder="audio",
                custom_filename=filename
            )
            
            logger.info(f"Stored audio file: {file_info.get('object_path')}")
            return file_info
            
        except Exception as e:
            logger.error(f"Failed to store audio file for {keyword}: {e}")
            return None
    
    async def generate_options_audio_batch(
        self,
        options: Dict[str, Any],
        task_id: str,
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Generate audio for multiple options in batch.
        
        Args:
            options: Dictionary of option key-value pairs
            task_id: Task ID for tracking
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dict[str, Dict[str, Any]]: Generated audio metadata for each option
        """
        try:
            options_metadata = {}
            total_options = len(options)
            
            logger.info(f"Generating audio for {total_options} options for task {task_id}")
            
            for i, (option_key, option_value) in enumerate(options.items(), 1):
                try:
                    if progress_callback:
                        progress_callback(i, total_options, option_key)
                    
                    # Generate audio for this option
                    audio_url, file_info, usage_metadata = await self.generate_audio(
                        str(option_value), "option_audio"
                    )
                    
                    if audio_url and file_info:
                        options_metadata[option_key] = {
                            "text": str(option_value),
                            "audio_url": audio_url,
                            "file_info": file_info,
                            "cache_id": f"{str(option_value).strip().lower()}_audio",
                            "generated_at": datetime.now(timezone.utc).isoformat(),
                            "usage_metadata": usage_metadata
                        }
                        
                        logger.info(f"Generated audio for option {i}/{total_options}: {option_key}")
                    else:
                        logger.error(f"Failed to generate audio for option {option_key}")
                        
                except Exception as e:
                    logger.error(f"Failed to generate audio for option {option_key}: {e}")
                    continue
            
            logger.info(f"Completed audio generation for {len(options_metadata)}/{total_options} options")
            return options_metadata
            
        except Exception as e:
            logger.error(f"Batch options audio generation failed: {e}")
            return {}
