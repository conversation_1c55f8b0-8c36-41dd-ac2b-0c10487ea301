"""
Media Manager - Unified Media Generation Coordinator

Coordinates all media generation activities across services with
intelligent caching, optimization, and service-specific configurations.
"""

import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from bson import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.helpers.media.cache_manager import CacheManager
from app.shared.helpers.media.audio_generator import AudioGenerator
from app.shared.helpers.media.image_generator import ImageGenerator

logger = setup_new_logging(__name__)


class MediaManager:
    """
    Unified media generation coordinator.
    
    Orchestrates audio and image generation across all services with
    intelligent caching, parallel processing, and service-specific optimizations.
    """
    
    def __init__(self, service_name: str, config_manager):
        """
        Initialize media manager.
        
        Args:
            service_name: Name of the service
            config_manager: Configuration manager instance
        """
        self.service_name = service_name
        self.config_manager = config_manager
        self.current_user = None
        
        # Initialize sub-managers
        self.cache_manager = CacheManager(service_name, config_manager)
        self.audio_generator = AudioGenerator(service_name, config_manager, self.cache_manager)
        self.image_generator = ImageGenerator(service_name, config_manager, self.cache_manager)
    
    async def initialize(self, current_user: UserTenantDB):
        """
        Initialize with user context.
        
        Args:
            current_user: User context for database operations
        """
        self.current_user = current_user
        
        # Initialize all sub-managers
        await self.cache_manager.initialize(current_user)
        await self.audio_generator.initialize(current_user)
        await self.image_generator.initialize(current_user)
        
        logger.info(f"MediaManager initialized for {self.service_name}")
    
    async def generate_task_media(
        self,
        task: Dict[str, Any],
        task_id: ObjectId,
        socketio_server: Optional[Any] = None
    ) -> bool:
        """
        Generate media (image/audio) for a task.
        
        Args:
            task: Task data
            task_id: Task ID
            socketio_server: Optional SocketIO server for notifications
            
        Returns:
            bool: Success status
        """
        try:
            if not self.current_user:
                raise ValueError("MediaManager not initialized with user context")
            
            # Get keyword from task
            keyword = task.get("question", {}).get("answer_hint", "")
            if not keyword:
                logger.warning(f"No keyword found for task {task_id}")
                return True  # Not an error, just no media to generate
            
            logger.info(f"Generating media for task {task_id} with keyword: {keyword}")
            
            # Generate image and audio in parallel if both are enabled
            tasks_to_run = []
            
            # Add image generation if enabled
            if self.image_generator.is_image_generation_enabled():
                tasks_to_run.append(self._generate_task_image(keyword, task_id))
            
            # Add audio generation if needed
            if self._is_audio_enabled():
                tasks_to_run.append(self._generate_task_audio(keyword, task_id))
            
            # Run tasks in parallel
            if tasks_to_run:
                results = await asyncio.gather(*tasks_to_run, return_exceptions=True)
                
                # Check results
                success_count = 0
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"Media generation task {i} failed: {result}")
                    elif result:
                        success_count += 1
                
                logger.info(f"Media generation completed: {success_count}/{len(tasks_to_run)} successful")
                return success_count > 0
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to generate task media for {task_id}: {e}")
            return False
    
    async def generate_options_audio(
        self,
        task: Dict[str, Any],
        task_id: ObjectId,
        socketio_server: Optional[Any] = None
    ) -> bool:
        """
        Generate audio for task options.
        
        Args:
            task: Task data
            task_id: Task ID
            socketio_server: Optional SocketIO server for notifications
            
        Returns:
            bool: Success status
        """
        try:
            if not self.current_user:
                raise ValueError("MediaManager not initialized with user context")
            
            # Get options from task
            options = task.get("question", {}).get("options")
            if not options:
                logger.info(f"No options found for task {task_id}")
                return True
            
            logger.info(f"Generating options audio for task {task_id}")
            
            # Update status to generating
            await self._update_options_audio_status(task_id, "generating")
            
            # Generate audio for all options
            def progress_callback(current, total, option_key):
                logger.info(f"Processing option {current}/{total}: {option_key}")
                if socketio_server and self.config_manager.is_socketio_enabled(self.service_name):
                    # Send progress update via SocketIO
                    pass  # Implementation depends on SocketIO structure
            
            options_metadata = await self.audio_generator.generate_options_audio_batch(
                options, str(task_id), progress_callback
            )
            
            # Update task with options metadata
            if options_metadata:
                await self._update_task_options_metadata(task_id, options_metadata)
                await self._update_options_audio_status(task_id, "completed")
                logger.info(f"Options audio generation completed for task {task_id}")
                return True
            else:
                await self._update_options_audio_status(task_id, "failed")
                return False
            
        except Exception as e:
            logger.error(f"Failed to generate options audio for {task_id}: {e}")
            await self._update_options_audio_status(task_id, "failed")
            return False
    
    async def _generate_task_image(self, keyword: str, task_id: ObjectId) -> bool:
        """Generate image for a task."""
        try:
            image_url, file_info, usage_metadata = await self.image_generator.generate_image(
                keyword, "task_image"
            )
            
            if image_url and file_info:
                # Update task with image metadata
                await self._update_task_image_metadata(task_id, {
                    "image_url": image_url,
                    "file_info": file_info,
                    "usage_metadata": usage_metadata
                })
                logger.info(f"Image generated for task {task_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to generate image for task {task_id}: {e}")
            return False
    
    async def _generate_task_audio(self, keyword: str, task_id: ObjectId) -> bool:
        """Generate audio for a task."""
        try:
            audio_url, file_info, usage_metadata = await self.audio_generator.generate_audio(
                keyword, "task_audio"
            )
            
            if audio_url and file_info:
                # Update task with audio metadata
                await self._update_task_audio_metadata(task_id, {
                    "audio_url": audio_url,
                    "file_info": file_info,
                    "usage_metadata": usage_metadata
                })
                logger.info(f"Audio generated for task {task_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to generate audio for task {task_id}: {e}")
            return False
    
    def _is_audio_enabled(self) -> bool:
        """Check if audio generation is enabled."""
        # This would check user preferences or service configuration
        return True  # Simplified for now
    
    async def _update_options_audio_status(self, task_id: ObjectId, status: str):
        """Update options audio generation status."""
        try:
            collection_name = self.config_manager.get_collection_name(self.service_name, "tasks")
            collection = self.current_user.async_db[collection_name]
            
            await collection.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "metadata.options_audio_status": status,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
        except Exception as e:
            logger.error(f"Failed to update options audio status: {e}")
    
    async def _update_task_options_metadata(self, task_id: ObjectId, options_metadata: Dict[str, Any]):
        """Update task with options metadata."""
        try:
            collection_name = self.config_manager.get_collection_name(self.service_name, "tasks")
            collection = self.current_user.async_db[collection_name]
            
            await collection.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "metadata.options_audio": options_metadata,
                        "metadata.options_audio_status": "completed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
        except Exception as e:
            logger.error(f"Failed to update task options metadata: {e}")
    
    async def _update_task_image_metadata(self, task_id: ObjectId, image_metadata: Dict[str, Any]):
        """Update task with image metadata."""
        try:
            collection_name = self.config_manager.get_collection_name(self.service_name, "tasks")
            collection = self.current_user.async_db[collection_name]
            
            await collection.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "metadata.image": image_metadata,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
        except Exception as e:
            logger.error(f"Failed to update task image metadata: {e}")
    
    async def _update_task_audio_metadata(self, task_id: ObjectId, audio_metadata: Dict[str, Any]):
        """Update task with audio metadata."""
        try:
            collection_name = self.config_manager.get_collection_name(self.service_name, "tasks")
            collection = self.current_user.async_db[collection_name]
            
            await collection.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "metadata.audio": audio_metadata,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
        except Exception as e:
            logger.error(f"Failed to update task audio metadata: {e}")
