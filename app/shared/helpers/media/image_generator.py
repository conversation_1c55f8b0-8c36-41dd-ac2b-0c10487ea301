"""
Image Generator - Unified Image Generation Across Services

Consolidates image generation functionality with intelligent caching
and service-specific configurations.
"""

from typing import Dict, Any, Optional
from datetime import datetime, timezone

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.helpers.media.cache_manager import CacheManager

logger = setup_new_logging(__name__)


class ImageGenerator:
    """
    Unified image generation with intelligent caching.
    
    Consolidates image generation functionality across services
    with consistent caching and optimization strategies.
    """
    
    def __init__(self, service_name: str, config_manager, cache_manager: CacheManager):
        """
        Initialize image generator.
        
        Args:
            service_name: Name of the service
            config_manager: Configuration manager instance
            cache_manager: Cache manager instance
        """
        self.service_name = service_name
        self.config_manager = config_manager
        self.cache_manager = cache_manager
        self.current_user = None
        self.image_enabled = None
    
    async def initialize(self, current_user: UserTenantDB):
        """
        Initialize with user context and service configuration.
        
        Args:
            current_user: User context for database operations
        """
        self.current_user = current_user
        
        # Get service-specific image configuration
        config = self.config_manager.get_config(self.service_name)
        self.image_enabled = config.media["image_enabled"]
        
        logger.info(f"ImageGenerator initialized for {self.service_name}, enabled: {self.image_enabled}")
    
    async def generate_image(
        self,
        keyword: str,
        image_type: str = "task_image",
        cache_enabled: bool = True
    ) -> tuple[Optional[str], Optional[Dict[str, Any]], Dict[str, Any]]:
        """
        Generate image with unified interface.
        
        Args:
            keyword: Text prompt for image generation
            image_type: Type of image generation
            cache_enabled: Whether to use caching
            
        Returns:
            tuple: (image_url, file_info, usage_metadata)
        """
        try:
            if not self.current_user:
                raise ValueError("ImageGenerator not initialized with user context")
            
            if not self.image_enabled:
                logger.info(f"Image generation disabled for {self.service_name}")
                return None, None, {}
            
            logger.info(f"Generating image for keyword: {keyword}")
            
            # Check cache first if enabled
            if cache_enabled:
                cached_image = await self.cache_manager.check_media_cache(
                    keyword, "image", image_type
                )
                if cached_image:
                    logger.info(f"Using cached image for keyword: {keyword}")
                    return (
                        cached_image["file_text"],
                        cached_image["file_info"],
                        cached_image["usage_metadata"]
                    )
            
            # Generate new image
            image_bytes, usage_metadata = await self._generate_image_bytes(keyword)
            
            if not image_bytes:
                logger.error(f"No image data received for: {keyword}")
                return None, None, usage_metadata
            
            # Store the image file
            file_info = await self._store_image_file(keyword, image_bytes, image_type)
            if not file_info:
                return None, None, usage_metadata
            
            # Cache the result if enabled
            if cache_enabled:
                await self.cache_manager.store_media_cache(
                    keyword, "image", file_info.get("object_path", ""), 
                    file_info, usage_metadata, image_type
                )
            
            return file_info.get("object_path", ""), file_info, usage_metadata
            
        except Exception as e:
            logger.error(f"Image generation failed for '{keyword}': {e}")
            return None, None, {}
    
    async def _generate_image_bytes(self, prompt: str) -> tuple[Optional[bytes], Dict[str, Any]]:
        """Generate image bytes using the configured image generation service."""
        try:
            # Import image generation function
            from app.shared.image_gen.dalle import generate_image_bytes
            
            # Generate image
            image_bytes = await generate_image_bytes(prompt)
            
            usage_metadata = {
                "engine": "dalle",
                "prompt": prompt,
                "prompt_length": len(prompt),
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
            
            return image_bytes, usage_metadata
            
        except Exception as e:
            logger.error(f"Image generation failed: {e}")
            return None, {}
    
    async def _store_image_file(
        self,
        keyword: str,
        image_bytes: bytes,
        image_type: str
    ) -> Optional[Dict[str, Any]]:
        """Store image file in Minio storage."""
        try:
            if not image_bytes:
                return None
            
            # Generate filename
            safe_keyword = "".join(c for c in keyword if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_keyword}_{image_type}.png"
            
            # Store in Minio
            file_info = self.current_user.minio_client.save_file(
                data=image_bytes,
                user_id=self.current_user.user_id,
                content_type="image/png",
                folder="images",
                custom_filename=filename
            )
            
            logger.info(f"Stored image file: {file_info.get('object_path')}")
            return file_info
            
        except Exception as e:
            logger.error(f"Failed to store image file for {keyword}: {e}")
            return None
    
    async def generate_thumbnail(
        self,
        keyword: str,
        cache_enabled: bool = True
    ) -> tuple[Optional[str], Optional[Dict[str, Any]]]:
        """
        Generate thumbnail image.
        
        Args:
            keyword: Text prompt for thumbnail
            cache_enabled: Whether to use caching
            
        Returns:
            tuple: (thumbnail_url, thumbnail_metadata)
        """
        try:
            image_url, file_info, usage_metadata = await self.generate_image(
                keyword, "thumbnail", cache_enabled
            )
            
            if image_url and file_info:
                thumbnail_metadata = {
                    "object_path": image_url,
                    "file_info": file_info,
                    "usage_metadata": usage_metadata,
                    "generated_at": datetime.now(timezone.utc).isoformat(),
                    "type": "thumbnail"
                }
                return image_url, thumbnail_metadata
            
            return None, None
            
        except Exception as e:
            logger.error(f"Thumbnail generation failed for '{keyword}': {e}")
            return None, None
    
    def is_image_generation_enabled(self) -> bool:
        """Check if image generation is enabled for the current service."""
        return self.image_enabled
    
    async def get_generation_stats(self) -> Dict[str, Any]:
        """
        Get image generation statistics.
        
        Returns:
            Dict[str, Any]: Generation statistics
        """
        try:
            cache_stats = await self.cache_manager.get_cache_stats()
            
            return {
                "service_name": self.service_name,
                "image_enabled": self.image_enabled,
                "cached_images": cache_stats.get("image_entries", 0),
                "cache_strategy": cache_stats.get("cache_strategy", "unknown")
            }
            
        except Exception as e:
            logger.error(f"Failed to get generation stats: {e}")
            return {}
