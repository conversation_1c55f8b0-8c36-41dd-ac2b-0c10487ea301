"""
Cache Manager for Intelligent Media Caching

Provides unified media caching with multi-user support and intelligent
cache strategies across all services.
"""

from typing import Dict, Any, Optional
from datetime import datetime, timezone

from app.shared.models.user import UserTenantDB
from app.shared.db_enums import CollectionName
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class CacheManager:
    """
    Intelligent media caching with multi-user support.
    
    Provides unified caching functionality that works across all services
    with different cache strategies and optimization levels.
    """
    
    def __init__(self, service_name: str, config_manager):
        """
        Initialize cache manager.
        
        Args:
            service_name: Name of the service
            config_manager: Configuration manager instance
        """
        self.service_name = service_name
        self.config_manager = config_manager
        self.current_user = None
        self.cache_strategy = None
    
    async def initialize(self, current_user: UserTenantDB):
        """
        Initialize with user context.
        
        Args:
            current_user: User context for database operations
        """
        self.current_user = current_user
        config = self.config_manager.get_config(self.service_name)
        self.cache_strategy = config.media["cache_strategy"]
        
        logger.info(f"CacheManager initialized for {self.service_name} with {self.cache_strategy} strategy")
    
    async def check_media_cache(
        self,
        keyword: str,
        media_type: str,
        cache_type: str = "standard"
    ) -> Optional[Dict[str, Any]]:
        """
        Check media cache with multi-user support.
        
        Args:
            keyword: Media keyword to search for
            media_type: Type of media (audio, image)
            cache_type: Type of cache check (standard, audio_prompt, etc.)
            
        Returns:
            Optional[Dict[str, Any]]: Cached media data if found
        """
        try:
            if not self.current_user:
                raise ValueError("CacheManager not initialized with user context")
            
            # Normalize keyword for consistent caching
            normalized_keyword = keyword.strip().lower()
            
            # Build cache query based on strategy
            cache_query = {
                "keyword": normalized_keyword,
                "media_type": media_type
            }
            
            # Add cache type for specific searches
            if cache_type != "standard":
                cache_query["cache_type"] = cache_type
            
            # Search in media collection
            media_collection = self.current_user.async_db[CollectionName.MEDIA]
            cached_item = await media_collection.find_one(cache_query)
            
            if cached_item:
                # Update access tracking
                await self._update_cache_access(cached_item["_id"])
                
                logger.info(f"Cache HIT for {media_type}: {keyword}")
                return {
                    "file_text": cached_item.get("file_text", ""),
                    "file_info": cached_item.get("file_info", {}),
                    "usage_metadata": cached_item.get("usage_metadata", {}),
                    "object_path": cached_item.get("object_path"),
                    "cached": True,
                    "cache_id": str(cached_item["_id"])
                }
            
            logger.info(f"Cache MISS for {media_type}: {keyword}")
            return None
            
        except Exception as e:
            logger.error(f"Cache check failed for {keyword}: {e}")
            return None
    
    async def store_media_cache(
        self,
        keyword: str,
        media_type: str,
        file_text: str,
        file_info: Dict[str, Any],
        usage_metadata: Dict[str, Any],
        cache_type: str = "standard"
    ) -> Optional[str]:
        """
        Store media in cache.
        
        Args:
            keyword: Media keyword
            media_type: Type of media (audio, image)
            file_text: File content or URL
            file_info: File metadata
            usage_metadata: Usage tracking data
            cache_type: Type of cache entry
            
        Returns:
            Optional[str]: Cache entry ID if successful
        """
        try:
            if not self.current_user:
                raise ValueError("CacheManager not initialized with user context")
            
            # Normalize keyword
            normalized_keyword = keyword.strip().lower()
            
            # Prepare cache document
            cache_doc = {
                "keyword": normalized_keyword,
                "media_type": media_type,
                "cache_type": cache_type,
                "file_text": file_text,
                "file_info": file_info,
                "usage_metadata": usage_metadata,
                "object_path": file_info.get("object_path"),
                "created_by": self.current_user.user_id,
                "created_at": datetime.now(timezone.utc),
                "last_accessed": datetime.now(timezone.utc),
                "access_count": 1,
                "service_source": self.service_name
            }
            
            # Store in media collection
            media_collection = self.current_user.async_db[CollectionName.MEDIA]
            result = await media_collection.insert_one(cache_doc)
            
            logger.info(f"Cached {media_type} for keyword: {keyword}")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"Failed to cache media for {keyword}: {e}")
            return None
    
    async def _update_cache_access(self, cache_id):
        """Update cache access tracking."""
        try:
            media_collection = self.current_user.async_db[CollectionName.MEDIA]
            await media_collection.update_one(
                {"_id": cache_id},
                {
                    "$set": {"last_accessed": datetime.now(timezone.utc)},
                    "$inc": {"access_count": 1}
                }
            )
        except Exception as e:
            logger.error(f"Failed to update cache access: {e}")
    
    async def invalidate_cache(self, keyword: str, media_type: str) -> bool:
        """
        Invalidate cache entries for a keyword.
        
        Args:
            keyword: Media keyword
            media_type: Type of media
            
        Returns:
            bool: Success status
        """
        try:
            if not self.current_user:
                raise ValueError("CacheManager not initialized with user context")
            
            normalized_keyword = keyword.strip().lower()
            
            media_collection = self.current_user.async_db[CollectionName.MEDIA]
            result = await media_collection.delete_many({
                "keyword": normalized_keyword,
                "media_type": media_type
            })
            
            logger.info(f"Invalidated {result.deleted_count} cache entries for {keyword}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to invalidate cache for {keyword}: {e}")
            return False
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dict[str, Any]: Cache statistics
        """
        try:
            if not self.current_user:
                raise ValueError("CacheManager not initialized with user context")
            
            media_collection = self.current_user.async_db[CollectionName.MEDIA]
            
            # Get basic stats
            total_entries = await media_collection.count_documents({})
            audio_entries = await media_collection.count_documents({"media_type": "audio"})
            image_entries = await media_collection.count_documents({"media_type": "image"})
            
            # Get service-specific stats
            service_entries = await media_collection.count_documents({"service_source": self.service_name})
            
            return {
                "total_entries": total_entries,
                "audio_entries": audio_entries,
                "image_entries": image_entries,
                "service_entries": service_entries,
                "cache_strategy": self.cache_strategy,
                "service_name": self.service_name
            }
            
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {}
