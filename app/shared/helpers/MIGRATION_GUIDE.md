# Migration Guide: Unified Helper System

This guide explains how to migrate existing services to use the new unified helper system, eliminating duplicate functionality and providing a consistent interface across all services.

## Overview

The unified helper system consolidates duplicate functionality from:
- Socket Service V2 (`task_utils_v2.py`)
- Editor Service (`split_and_manage.py`)
- Followup Service (imports from `task_utils_v2.py`)

## Key Benefits

- ✅ **Single Source of Truth**: No more duplicate functions
- ✅ **Consistent Interface**: Same API across all services
- ✅ **Service-Specific Configs**: Each service can have custom behaviors
- ✅ **Intelligent Caching**: Shared media cache with optimization
- ✅ **Centralized Monitoring**: Unified logging and error handling
- ✅ **Easy Maintenance**: Changes in one place affect all services

## Migration Steps

### 1. Import the Unified Helper System

```python
# Replace individual imports with unified system
from app.shared.helpers import TaskProcessor

# Initialize for your service
processor = TaskProcessor("socket_service_v2")  # or "editor_service", "followup_service"
await processor.initialize(current_user)
```

### 2. Replace Function Calls

#### Socket Service V2 Migration

**Before (task_utils_v2.py):**
```python
# Multiple separate function calls
await _generate_task_media(current_user, task, task_id, socketio_server)
await _generate_options_audio_sequential(current_user, task, task_id, socketio_server)
await _update_task_generation_completion_status(current_user, task_id, task_type)
await _check_and_generate_thumbnail(current_user, task_id)
```

**After (unified helpers):**
```python
# Single unified interface
processor = TaskProcessor("socket_service_v2")
await processor.initialize(current_user)

# Process complete task with all media generation
success = await processor._process_single_task(current_user, task, task_id, socketio_server)

# Or individual operations if needed
await processor.process_task_media_generation(current_user, task, task_id, socketio_server)
await processor.process_options_audio_generation(current_user, task, task_id, socketio_server)
await processor.process_task_set_completion(current_user, task_set_id, check_thumbnail=True)
```

#### Editor Service Migration

**Before (split_and_manage.py):**
```python
# Complex media generation logic
await generate_media_for_tasks_and_stories(
    current_user, task_item_ids, story_item_ids,
    generate_image, generate_audio_task
)
await _generate_curated_options_audio(
    current_user, task_item, task_id, generate_audio_task_func
)
```

**After (unified helpers):**
```python
# Simplified batch processing
processor = TaskProcessor("editor_service")
await processor.initialize(current_user)

results = await processor.process_batch_tasks(
    current_user, task_items, task_ids, socketio_server=None, parallel=True
)
```

#### Followup Service Migration

**Before (followup_simple.py):**
```python
# Importing from socket service
from app.v2.api.socket_service_v2.generator.task_utils_v2 import _generate_task_media, _generate_options_audio_sequential

await _generate_task_media(current_user, task, task_id, None)
await _generate_options_audio_sequential(current_user, task, task_id, None)
```

**After (unified helpers):**
```python
# Use same interface as other services
processor = TaskProcessor("followup_service")
await processor.initialize(current_user)

success = await processor._process_single_task(current_user, task, task_id, None)
```

### 3. Update Configuration

The unified system automatically handles service-specific configurations:

- **Socket Service V2**: Uses EdgeTTS, aggressive caching, priority mode
- **Editor Service**: Uses PiperTTS, standard caching, batch processing
- **Followup Service**: Uses EdgeTTS, standard caching, sequential processing

No manual configuration needed - the system detects the service and applies appropriate settings.

### 4. Error Handling

The unified system provides consistent error handling:

```python
try:
    processor = TaskProcessor("your_service_name")
    await processor.initialize(current_user)
    
    success = await processor.process_task_media_generation(
        current_user, task, task_id, socketio_server
    )
    
    if not success:
        logger.error(f"Media generation failed for task {task_id}")
        
except Exception as e:
    logger.error(f"Task processing error: {e}")
    # Unified error handling and recovery
```

## Service-Specific Configurations

### Socket Service V2 Config
```python
{
    "service_name": "socket_service_v2",
    "collections": {"tasks": "task_items", "sets": "task_sets"},
    "media": {
        "audio_engine": "edgetts",
        "cache_strategy": "aggressive",
        "image_enabled": True
    },
    "processing": {
        "priority_mode": True,
        "sequential_audio": True,
        "socketio_enabled": True
    }
}
```

### Editor Service Config
```python
{
    "service_name": "editor_service",
    "collections": {"tasks": "curated_content_items", "sets": "curated_content_set"},
    "media": {
        "audio_engine": "pipertts",
        "cache_strategy": "standard",
        "image_enabled": True
    },
    "processing": {
        "priority_mode": False,
        "sequential_audio": True,
        "socketio_enabled": False
    }
}
```

### Followup Service Config
```python
{
    "service_name": "followup_service",
    "collections": {"tasks": "task_items", "sets": "task_sets"},
    "media": {
        "audio_engine": "edgetts",
        "cache_strategy": "standard",
        "image_enabled": True
    },
    "processing": {
        "priority_mode": False,
        "background_tasks": False,
        "socketio_enabled": False
    }
}
```

## Testing Migration

### 1. Unit Tests
```python
import pytest
from app.shared.helpers import TaskProcessor

@pytest.mark.asyncio
async def test_socket_service_migration(current_user, sample_task):
    processor = TaskProcessor("socket_service_v2")
    await processor.initialize(current_user)
    
    success = await processor.process_task_media_generation(
        current_user, sample_task, sample_task["_id"]
    )
    
    assert success is True
```

### 2. Integration Tests
```python
@pytest.mark.asyncio
async def test_cross_service_consistency(current_user, sample_task):
    # Test that all services produce consistent results
    services = ["socket_service_v2", "editor_service", "followup_service"]
    
    for service_name in services:
        processor = TaskProcessor(service_name)
        await processor.initialize(current_user)
        
        # Each service should handle the same task consistently
        success = await processor.process_task_media_generation(
            current_user, sample_task, sample_task["_id"]
        )
        
        assert success is True
```

## Rollback Plan

If issues arise during migration:

1. **Gradual Migration**: Migrate one service at a time
2. **Feature Flags**: Use feature flags to toggle between old and new systems
3. **Monitoring**: Monitor performance and error rates during migration
4. **Quick Rollback**: Keep old functions available during transition period

```python
# Feature flag example
USE_UNIFIED_HELPERS = os.getenv("USE_UNIFIED_HELPERS", "false").lower() == "true"

if USE_UNIFIED_HELPERS:
    # Use new unified system
    processor = TaskProcessor("socket_service_v2")
    await processor.initialize(current_user)
    success = await processor.process_task_media_generation(current_user, task, task_id)
else:
    # Use old system
    await _generate_task_media(current_user, task, task_id, socketio_server)
```

## Performance Considerations

- **Caching**: Unified cache reduces redundant media generation
- **Parallel Processing**: Batch operations use parallel processing where appropriate
- **Memory Usage**: Shared components reduce memory footprint
- **Database Connections**: Optimized database operations with connection reuse

## Monitoring and Debugging

The unified system provides enhanced monitoring:

```python
# Get system statistics
from app.shared.helpers import ConfigManager, MediaManager

config_manager = ConfigManager()
media_manager = MediaManager("socket_service_v2", config_manager)
await media_manager.initialize(current_user)

# Cache statistics
cache_stats = await media_manager.cache_manager.get_cache_stats()
print(f"Cache efficiency: {cache_stats}")

# Database statistics
db_stats = await media_manager.db_manager.get_collection_stats()
print(f"Database usage: {db_stats}")
```

## Next Steps

1. **Start with Followup Service**: Simplest migration (just change imports)
2. **Move to Socket Service**: Replace task_utils_v2 functions gradually
3. **Finish with Editor Service**: Most complex due to batch processing
4. **Remove Old Code**: After successful migration, remove duplicate functions
5. **Monitor Performance**: Track improvements in cache hit rates and processing speed
