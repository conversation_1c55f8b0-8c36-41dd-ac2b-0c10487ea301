"""
Storage Manager - Unified File Storage with Metadata Management

Provides unified file storage operations with comprehensive metadata
management and service-specific organization.
"""

from typing import Dict, Any, Optional
from datetime import datetime, timezone
import hashlib

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class StorageManager:
    """
    Unified file storage with metadata management.
    
    Provides consistent file storage operations across all services
    with comprehensive metadata tracking and organization.
    """
    
    def __init__(self, service_name: str, config_manager):
        """
        Initialize storage manager.
        
        Args:
            service_name: Name of the service
            config_manager: Configuration manager instance
        """
        self.service_name = service_name
        self.config_manager = config_manager
        self.current_user = None
    
    async def initialize(self, current_user: UserTenantDB):
        """
        Initialize with user context.
        
        Args:
            current_user: User context for storage operations
        """
        self.current_user = current_user
        logger.info(f"StorageManager initialized for {self.service_name}")
    
    async def store_file(
        self,
        data: bytes,
        filename: str,
        content_type: str,
        folder: str = "files",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Store file with comprehensive metadata.
        
        Args:
            data: File data bytes
            filename: Desired filename
            content_type: MIME type
            folder: Storage folder
            metadata: Additional metadata
            
        Returns:
            Optional[Dict[str, Any]]: File information if successful
        """
        try:
            if not self.current_user:
                raise ValueError("StorageManager not initialized with user context")
            
            if not data:
                raise ValueError("No data provided for storage")
            
            # Generate file hash for deduplication
            file_hash = hashlib.sha256(data).hexdigest()
            
            # Prepare enhanced metadata
            enhanced_metadata = {
                "service_name": self.service_name,
                "file_hash": file_hash,
                "file_size": len(data),
                "uploaded_at": datetime.now(timezone.utc).isoformat(),
                "content_type": content_type,
                "folder": folder
            }
            
            # Add custom metadata
            if metadata:
                enhanced_metadata.update(metadata)
            
            # Store file using Minio client
            file_info = self.current_user.minio_client.save_file(
                data=data,
                user_id=self.current_user.user_id,
                content_type=content_type,
                folder=folder,
                custom_filename=filename
            )
            
            # Enhance file info with our metadata
            if file_info:
                file_info["enhanced_metadata"] = enhanced_metadata
                file_info["service_source"] = self.service_name
                
                logger.info(f"Stored file: {file_info.get('object_path')} ({len(data)} bytes)")
            
            return file_info
            
        except Exception as e:
            logger.error(f"Failed to store file {filename}: {e}")
            return None
    
    async def store_audio_file(
        self,
        audio_data: bytes,
        keyword: str,
        audio_type: str = "audio",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Store audio file with audio-specific metadata.
        
        Args:
            audio_data: Audio data bytes
            keyword: Audio keyword/text
            audio_type: Type of audio (task_audio, option_audio, etc.)
            metadata: Additional metadata
            
        Returns:
            Optional[Dict[str, Any]]: File information if successful
        """
        try:
            # Generate safe filename
            safe_keyword = "".join(c for c in keyword if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_keyword}_{audio_type}.wav"
            
            # Prepare audio-specific metadata
            audio_metadata = {
                "media_type": "audio",
                "audio_type": audio_type,
                "keyword": keyword,
                "text_content": keyword,
                "duration_estimate": len(audio_data) / 16000,  # Rough estimate
            }
            
            # Merge with provided metadata
            if metadata:
                audio_metadata.update(metadata)
            
            return await self.store_file(
                data=audio_data,
                filename=filename,
                content_type="audio/wav",
                folder="audio",
                metadata=audio_metadata
            )
            
        except Exception as e:
            logger.error(f"Failed to store audio file for keyword '{keyword}': {e}")
            return None
    
    async def store_image_file(
        self,
        image_data: bytes,
        keyword: str,
        image_type: str = "image",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Store image file with image-specific metadata.
        
        Args:
            image_data: Image data bytes
            keyword: Image keyword/prompt
            image_type: Type of image (task_image, thumbnail, etc.)
            metadata: Additional metadata
            
        Returns:
            Optional[Dict[str, Any]]: File information if successful
        """
        try:
            # Generate safe filename
            safe_keyword = "".join(c for c in keyword if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_keyword}_{image_type}.png"
            
            # Prepare image-specific metadata
            image_metadata = {
                "media_type": "image",
                "image_type": image_type,
                "keyword": keyword,
                "prompt": keyword,
                "estimated_size": len(image_data),
            }
            
            # Merge with provided metadata
            if metadata:
                image_metadata.update(metadata)
            
            return await self.store_file(
                data=image_data,
                filename=filename,
                content_type="image/png",
                folder="images",
                metadata=image_metadata
            )
            
        except Exception as e:
            logger.error(f"Failed to store image file for keyword '{keyword}': {e}")
            return None
    
    async def get_file_info(self, object_path: str) -> Optional[Dict[str, Any]]:
        """
        Get file information by object path.
        
        Args:
            object_path: Object path in storage
            
        Returns:
            Optional[Dict[str, Any]]: File information if found
        """
        try:
            if not self.current_user:
                raise ValueError("StorageManager not initialized with user context")
            
            # This would need to be implemented based on Minio client capabilities
            # For now, return basic info
            return {
                "object_path": object_path,
                "service_source": self.service_name,
                "accessible": True
            }
            
        except Exception as e:
            logger.error(f"Failed to get file info for {object_path}: {e}")
            return None
    
    async def delete_file(self, object_path: str) -> bool:
        """
        Delete file from storage.
        
        Args:
            object_path: Object path in storage
            
        Returns:
            bool: Success status
        """
        try:
            if not self.current_user:
                raise ValueError("StorageManager not initialized with user context")
            
            # This would need to be implemented based on Minio client capabilities
            logger.info(f"File deletion requested for: {object_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete file {object_path}: {e}")
            return False
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get storage statistics for the service.
        
        Returns:
            Dict[str, Any]: Storage statistics
        """
        try:
            # This would need to be implemented based on actual storage usage tracking
            return {
                "service_name": self.service_name,
                "total_files": 0,  # Placeholder
                "total_size": 0,   # Placeholder
                "audio_files": 0,  # Placeholder
                "image_files": 0,  # Placeholder
            }
            
        except Exception as e:
            logger.error(f"Failed to get storage stats: {e}")
            return {}
    
    def generate_filename(
        self,
        base_name: str,
        file_type: str,
        extension: str = None
    ) -> str:
        """
        Generate a safe filename with service prefix.
        
        Args:
            base_name: Base name for the file
            file_type: Type of file
            extension: File extension (auto-detected if not provided)
            
        Returns:
            str: Generated filename
        """
        try:
            # Sanitize base name
            safe_base = "".join(c for c in base_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            
            # Add service prefix
            service_prefix = self.service_name.replace("_", "-")
            
            # Generate timestamp
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            
            # Determine extension
            if not extension:
                if file_type.startswith("audio"):
                    extension = "wav"
                elif file_type.startswith("image"):
                    extension = "png"
                else:
                    extension = "bin"
            
            return f"{service_prefix}_{safe_base}_{file_type}_{timestamp}.{extension}"
            
        except Exception as e:
            logger.error(f"Failed to generate filename: {e}")
            return f"file_{file_type}_{datetime.now(timezone.utc).timestamp()}.bin"
