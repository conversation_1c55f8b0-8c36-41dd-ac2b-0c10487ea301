"""
Database Manager - Unified Database Operations

Provides abstracted database operations that work across different
collections and services with consistent patterns and error handling.
"""

from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone
from bson import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class DatabaseManager:
    """
    Unified database operations with collection abstraction.
    
    Provides consistent database operations across different services
    while handling service-specific collection names and field mappings.
    """
    
    def __init__(self, service_name: str, config_manager):
        """
        Initialize database manager.
        
        Args:
            service_name: Name of the service
            config_manager: Configuration manager instance
        """
        self.service_name = service_name
        self.config_manager = config_manager
        self.current_user = None
        self.collections = None
        self.field_mappings = None
    
    async def initialize(self, current_user: UserTenantDB):
        """
        Initialize with user context and service configuration.
        
        Args:
            current_user: User context for database operations
        """
        self.current_user = current_user
        
        # Get service-specific configuration
        config = self.config_manager.get_config(self.service_name)
        self.collections = config.collections
        self.field_mappings = config.database
        
        logger.info(f"DatabaseManager initialized for {self.service_name}")
    
    async def update_task_status(
        self,
        task_id: ObjectId,
        status: str,
        additional_fields: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update task status with service-specific field mapping.
        
        Args:
            task_id: Task ID
            status: New status value
            additional_fields: Additional fields to update
            
        Returns:
            bool: Success status
        """
        try:
            if not self.current_user:
                raise ValueError("DatabaseManager not initialized with user context")
            
            # Get the appropriate collection
            collection_name = self.collections["tasks"]
            collection = self.current_user.async_db[collection_name]
            
            # Build update document
            update_doc = {
                "$set": {
                    self.field_mappings["status_field"]: status,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
            
            # Add additional fields if provided
            if additional_fields:
                update_doc["$set"].update(additional_fields)
            
            # Perform update
            result = await collection.update_one(
                {"_id": task_id},
                update_doc
            )
            
            if result.modified_count > 0:
                logger.info(f"Updated task {task_id} status to {status}")
                return True
            else:
                logger.warning(f"No task found with ID {task_id}")
                return False
            
        except Exception as e:
            logger.error(f"Failed to update task status for {task_id}: {e}")
            return False
    
    async def update_task_set_status(
        self,
        task_set_id: str,
        status: str,
        additional_fields: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update task set status.
        
        Args:
            task_set_id: Task set ID
            status: New status value
            additional_fields: Additional fields to update
            
        Returns:
            bool: Success status
        """
        try:
            if not self.current_user:
                raise ValueError("DatabaseManager not initialized with user context")
            
            # Get the appropriate collection
            collection_name = self.collections["sets"]
            collection = self.current_user.async_db[collection_name]
            
            # Build update document
            update_doc = {
                "$set": {
                    "status": status,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
            
            # Add additional fields if provided
            if additional_fields:
                update_doc["$set"].update(additional_fields)
            
            # Perform update
            result = await collection.update_one(
                {"_id": ObjectId(task_set_id)},
                update_doc
            )
            
            if result.modified_count > 0:
                logger.info(f"Updated task set {task_set_id} status to {status}")
                return True
            else:
                logger.warning(f"No task set found with ID {task_set_id}")
                return False
            
        except Exception as e:
            logger.error(f"Failed to update task set status for {task_set_id}: {e}")
            return False
    
    async def get_task(self, task_id: ObjectId) -> Optional[Dict[str, Any]]:
        """
        Get task by ID.
        
        Args:
            task_id: Task ID
            
        Returns:
            Optional[Dict[str, Any]]: Task data if found
        """
        try:
            if not self.current_user:
                raise ValueError("DatabaseManager not initialized with user context")
            
            collection_name = self.collections["tasks"]
            collection = self.current_user.async_db[collection_name]
            
            task = await collection.find_one({"_id": task_id})
            return task
            
        except Exception as e:
            logger.error(f"Failed to get task {task_id}: {e}")
            return None
    
    async def get_task_set(self, task_set_id: str) -> Optional[Dict[str, Any]]:
        """
        Get task set by ID.
        
        Args:
            task_set_id: Task set ID
            
        Returns:
            Optional[Dict[str, Any]]: Task set data if found
        """
        try:
            if not self.current_user:
                raise ValueError("DatabaseManager not initialized with user context")
            
            collection_name = self.collections["sets"]
            collection = self.current_user.async_db[collection_name]
            
            task_set = await collection.find_one({"_id": ObjectId(task_set_id)})
            return task_set
            
        except Exception as e:
            logger.error(f"Failed to get task set {task_set_id}: {e}")
            return None
    
    async def get_tasks_by_set_id(self, task_set_id: str) -> List[Dict[str, Any]]:
        """
        Get all tasks for a task set.
        
        Args:
            task_set_id: Task set ID
            
        Returns:
            List[Dict[str, Any]]: List of tasks
        """
        try:
            if not self.current_user:
                raise ValueError("DatabaseManager not initialized with user context")
            
            collection_name = self.collections["tasks"]
            collection = self.current_user.async_db[collection_name]
            
            # Query depends on service type
            if self.service_name == "editor_service":
                # For editor service, tasks are linked via task_set_id
                query = {"task_set_id": ObjectId(task_set_id)}
            else:
                # For socket service, tasks are linked via collection_id
                query = {"collection_id": ObjectId(task_set_id)}
            
            tasks = await collection.find(query).to_list(length=None)
            return tasks
            
        except Exception as e:
            logger.error(f"Failed to get tasks for set {task_set_id}: {e}")
            return []
    
    async def check_all_tasks_completed(self, task_ids: List[ObjectId]) -> bool:
        """
        Check if all tasks in a list are completed.
        
        Args:
            task_ids: List of task IDs
            
        Returns:
            bool: True if all tasks are completed
        """
        try:
            if not self.current_user or not task_ids:
                return False
            
            collection_name = self.collections["tasks"]
            collection = self.current_user.async_db[collection_name]
            
            # Count completed tasks
            completed_count = await collection.count_documents({
                "_id": {"$in": task_ids},
                self.field_mappings["status_field"]: "completed"
            })
            
            return completed_count == len(task_ids)
            
        except Exception as e:
            logger.error(f"Failed to check task completion status: {e}")
            return False
    
    async def update_multiple_tasks(
        self,
        task_ids: List[ObjectId],
        update_fields: Dict[str, Any]
    ) -> int:
        """
        Update multiple tasks with the same fields.
        
        Args:
            task_ids: List of task IDs
            update_fields: Fields to update
            
        Returns:
            int: Number of tasks updated
        """
        try:
            if not self.current_user or not task_ids:
                return 0
            
            collection_name = self.collections["tasks"]
            collection = self.current_user.async_db[collection_name]
            
            # Add timestamp
            update_fields["updated_at"] = datetime.now(timezone.utc)
            
            # Perform bulk update
            result = await collection.update_many(
                {"_id": {"$in": task_ids}},
                {"$set": update_fields}
            )
            
            logger.info(f"Updated {result.modified_count} tasks")
            return result.modified_count
            
        except Exception as e:
            logger.error(f"Failed to update multiple tasks: {e}")
            return 0
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get statistics for the service collections.
        
        Returns:
            Dict[str, Any]: Collection statistics
        """
        try:
            if not self.current_user:
                raise ValueError("DatabaseManager not initialized with user context")
            
            stats = {}
            
            # Get task collection stats
            task_collection = self.current_user.async_db[self.collections["tasks"]]
            stats["total_tasks"] = await task_collection.count_documents({})
            stats["completed_tasks"] = await task_collection.count_documents({
                self.field_mappings["status_field"]: "completed"
            })
            
            # Get task set collection stats
            set_collection = self.current_user.async_db[self.collections["sets"]]
            stats["total_sets"] = await set_collection.count_documents({})
            
            stats["service_name"] = self.service_name
            stats["collections"] = self.collections
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {}
