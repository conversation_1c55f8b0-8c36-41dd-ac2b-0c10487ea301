"""
Status Manager - Centralized Status Tracking and Completion Handling

Manages task and task set completion status, thumbnail generation triggers,
and other status-related operations across all services.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from bson import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.helpers.database.db_manager import DatabaseManager

logger = setup_new_logging(__name__)


class StatusManager:
    """
    Centralized status tracking and completion handling.
    
    Manages task completion status, thumbnail generation triggers,
    and other status-related operations with service-specific logic.
    """
    
    def __init__(self, service_name: str, config_manager):
        """
        Initialize status manager.
        
        Args:
            service_name: Name of the service
            config_manager: Configuration manager instance
        """
        self.service_name = service_name
        self.config_manager = config_manager
        self.current_user = None
        self.db_manager = None
        self.thumbnail_enabled = None
    
    async def initialize(self, current_user: UserTenantDB):
        """
        Initialize with user context and service configuration.
        
        Args:
            current_user: User context for database operations
        """
        self.current_user = current_user
        self.db_manager = DatabaseManager(self.service_name, self.config_manager)
        await self.db_manager.initialize(current_user)
        
        # Get service configuration
        config = self.config_manager.get_config(self.service_name)
        self.thumbnail_enabled = config.processing["thumbnail_generation"]
        
        logger.info(f"StatusManager initialized for {self.service_name}")
    
    async def update_task_completion_status(
        self,
        task_id: ObjectId,
        task_type: str,
        trigger_thumbnail: bool = True
    ) -> bool:
        """
        Update task completion status and trigger thumbnail generation if needed.
        
        Args:
            task_id: Task ID
            task_type: Type of task
            trigger_thumbnail: Whether to trigger thumbnail generation check
            
        Returns:
            bool: Success status
        """
        try:
            if not self.current_user:
                raise ValueError("StatusManager not initialized with user context")
            
            # Update task status to completed
            success = await self.db_manager.update_task_status(
                task_id,
                "completed",
                {
                    "completed_at": datetime.now(timezone.utc).isoformat(),
                    "task_type": task_type
                }
            )
            
            if success and trigger_thumbnail and self.thumbnail_enabled:
                # Check if thumbnail generation is needed
                await self.check_and_generate_thumbnail_for_task(task_id)
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to update task completion status for {task_id}: {e}")
            return False
    
    async def check_and_generate_thumbnail_for_task(self, task_id: ObjectId) -> bool:
        """
        Check if thumbnail generation is needed for a task's parent set.
        
        Args:
            task_id: Task ID
            
        Returns:
            bool: Success status
        """
        try:
            # Get task to find its parent set
            task = await self.db_manager.get_task(task_id)
            if not task:
                return False
            
            # Get task set ID based on service type
            if self.service_name == "editor_service":
                task_set_id = task.get("task_set_id")
            else:
                task_set_id = task.get("collection_id")
            
            if task_set_id:
                return await self.check_and_generate_thumbnail(str(task_set_id))
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to check thumbnail for task {task_id}: {e}")
            return False
    
    async def check_and_generate_thumbnail(self, task_set_id: str) -> bool:
        """
        Check if thumbnail generation is needed for a task set.
        
        Args:
            task_set_id: Task set ID
            
        Returns:
            bool: Success status
        """
        try:
            if not self.current_user:
                raise ValueError("StatusManager not initialized with user context")
            
            # Get task set
            task_set = await self.db_manager.get_task_set(task_set_id)
            if not task_set:
                logger.warning(f"Task set {task_set_id} not found")
                return False
            
            # Skip if thumbnail already exists
            if task_set.get("thumbnail_metadata"):
                logger.info(f"Thumbnail already exists for task set {task_set_id}")
                return True
            
            # Skip if no thumbnail keyword is set
            thumbnail_keyword = task_set.get("thumbnail")
            if not thumbnail_keyword:
                logger.info(f"No thumbnail keyword set for task set {task_set_id}")
                return True
            
            # Check if all tasks are completed
            task_ids = task_set.get("tasks", [])
            if task_ids:
                all_tasks_completed = await self.db_manager.check_all_tasks_completed(task_ids)
                if not all_tasks_completed:
                    logger.info(f"Not all tasks completed for task set {task_set_id}")
                    return True
            
            # Check if all stories are completed (if applicable)
            story_ids = task_set.get("stories", [])
            if story_ids:
                all_stories_completed = await self._check_all_stories_completed(story_ids)
                if not all_stories_completed:
                    logger.info(f"Not all stories completed for task set {task_set_id}")
                    return True
            
            # Generate thumbnail
            return await self._generate_thumbnail(task_set_id, thumbnail_keyword)
            
        except Exception as e:
            logger.error(f"Failed to check and generate thumbnail for {task_set_id}: {e}")
            return False
    
    async def _check_all_stories_completed(self, story_ids: List[ObjectId]) -> bool:
        """Check if all stories are completed."""
        try:
            if not story_ids:
                return True
            
            # Get story collection based on service
            if self.service_name == "editor_service":
                collection_name = "curated_story_items"
            else:
                collection_name = "story_steps"
            
            collection = self.current_user.async_db[collection_name]
            
            # Count completed stories
            completed_count = await collection.count_documents({
                "_id": {"$in": story_ids},
                "metadata.status": "completed"
            })
            
            return completed_count == len(story_ids)
            
        except Exception as e:
            logger.error(f"Failed to check story completion: {e}")
            return False
    
    async def _generate_thumbnail(self, task_set_id: str, thumbnail_keyword: str) -> bool:
        """Generate thumbnail for a task set."""
        try:
            logger.info(f"Generating thumbnail for task set {task_set_id} with keyword: {thumbnail_keyword}")
            
            # Import image generation function
            from app.shared.helpers.media.image_generator import ImageGenerator
            from app.shared.helpers.media.cache_manager import CacheManager
            
            # Create image generator
            cache_manager = CacheManager(self.service_name, self.config_manager)
            await cache_manager.initialize(self.current_user)
            
            image_generator = ImageGenerator(self.service_name, self.config_manager, cache_manager)
            await image_generator.initialize(self.current_user)
            
            # Generate thumbnail
            thumbnail_url, thumbnail_metadata = await image_generator.generate_thumbnail(
                thumbnail_keyword
            )
            
            if thumbnail_url and thumbnail_metadata:
                # Update task set with thumbnail
                success = await self.db_manager.update_task_set_status(
                    task_set_id,
                    "completed",
                    {
                        "thumbnail": thumbnail_url,
                        "thumbnail_metadata": thumbnail_metadata
                    }
                )
                
                if success:
                    logger.info(f"Thumbnail generated and saved for task set {task_set_id}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to generate thumbnail for {task_set_id}: {e}")
            return False
    
    async def mark_followup_level_completed(
        self,
        original_task_set_id: str,
        level: int
    ) -> bool:
        """
        Mark a followup level as completed in the original task set.
        
        Args:
            original_task_set_id: Original task set ID
            level: Followup level
            
        Returns:
            bool: Success status
        """
        try:
            if not self.current_user:
                raise ValueError("StatusManager not initialized with user context")
            
            # Get the appropriate collection
            collection_name = self.config_manager.get_collection_name(self.service_name, "sets")
            collection = self.current_user.async_db[collection_name]
            
            # Update followup completion status
            result = await collection.update_one(
                {"_id": ObjectId(original_task_set_id)},
                {
                    "$set": {
                        f"followup_levels.level_{level}.completed": True,
                        f"followup_levels.level_{level}.completed_at": datetime.now(timezone.utc).isoformat(),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            if result.modified_count > 0:
                logger.info(f"Marked followup level {level} as completed for task set {original_task_set_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to mark followup level completed: {e}")
            return False
    
    async def get_completion_stats(self, task_set_id: str) -> Dict[str, Any]:
        """
        Get completion statistics for a task set.
        
        Args:
            task_set_id: Task set ID
            
        Returns:
            Dict[str, Any]: Completion statistics
        """
        try:
            if not self.current_user:
                raise ValueError("StatusManager not initialized with user context")
            
            # Get task set
            task_set = await self.db_manager.get_task_set(task_set_id)
            if not task_set:
                return {}
            
            # Get tasks
            tasks = await self.db_manager.get_tasks_by_set_id(task_set_id)
            
            # Calculate stats
            total_tasks = len(tasks)
            completed_tasks = sum(1 for task in tasks if task.get("metadata", {}).get("status") == "completed")
            
            stats = {
                "task_set_id": task_set_id,
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "completion_percentage": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0,
                "has_thumbnail": bool(task_set.get("thumbnail_metadata")),
                "service_name": self.service_name
            }
            
            # Add story stats if applicable
            story_ids = task_set.get("stories", [])
            if story_ids:
                # This would need to be implemented based on story collection structure
                stats["total_stories"] = len(story_ids)
                stats["completed_stories"] = 0  # Placeholder
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get completion stats for {task_set_id}: {e}")
            return {}
