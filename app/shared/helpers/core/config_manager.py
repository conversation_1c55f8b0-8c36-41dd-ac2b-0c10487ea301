"""
Configuration Manager for Service-Specific Settings

Handles service detection and configuration loading for different services
(socket_service_v2, editor_service, followup_service) with their specific
requirements and behaviors.
"""

from typing import Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass

from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class ServiceType(Enum):
    """Supported service types."""
    SOCKET_SERVICE_V2 = "socket_service_v2"
    EDITOR_SERVICE = "editor_service"
    FOLLOWUP_SERVICE = "followup_service"
    MANAGEMENT_SERVICE = "management_service"


@dataclass
class ServiceConfig:
    """Service-specific configuration."""
    service_name: str
    collections: Dict[str, str]
    media: Dict[str, Any]
    processing: Dict[str, Any]
    database: Dict[str, Any]


class ConfigManager:
    """
    Manages service-specific configurations and behaviors.
    
    Provides a centralized way to handle different service requirements
    while maintaining a unified interface for common operations.
    """
    
    def __init__(self):
        self._configs = self._load_service_configs()
    
    def _load_service_configs(self) -> Dict[str, ServiceConfig]:
        """Load all service configurations."""
        return {
            ServiceType.SOCKET_SERVICE_V2.value: ServiceConfig(
                service_name="socket_service_v2",
                collections={
                    "tasks": "task_items",
                    "sets": "task_sets",
                    "media": "media"
                },
                media={
                    "audio_engine": "edgetts",
                    "image_enabled": True,
                    "cache_strategy": "aggressive",
                    "audio_params": {
                        "language": "ne",
                        "gender": "Female",
                        "length_scale": 1.25,
                        "noise_scale": 0.667,
                        "noise_w": 0.8,
                        "sentence_silence": 0.5
                    }
                },
                processing={
                    "priority_mode": True,
                    "sequential_audio": True,
                    "background_tasks": True,
                    "socketio_enabled": True,
                    "thumbnail_generation": True
                },
                database={
                    "status_field": "metadata.status",
                    "options_audio_field": "metadata.options_audio_status",
                    "completion_field": "metadata.completion_status"
                }
            ),
            
            ServiceType.EDITOR_SERVICE.value: ServiceConfig(
                service_name="editor_service",
                collections={
                    "tasks": "curated_content_items",
                    "sets": "curated_content_set",
                    "media": "media"
                },
                media={
                    "audio_engine": "pipertts",
                    "image_enabled": True,
                    "cache_strategy": "standard",
                    "audio_params": {
                        "gender": "ne-female-400",
                        "speaker": 0,
                        "length_scale": 1.15,
                        "noise_scale": 0.667,
                        "noise_w": 0.8,
                        "sentence_silence": 0.5
                    }
                },
                processing={
                    "priority_mode": False,
                    "sequential_audio": True,
                    "background_tasks": True,
                    "socketio_enabled": False,
                    "thumbnail_generation": True
                },
                database={
                    "status_field": "metadata.status",
                    "options_audio_field": "metadata.options_audio_status",
                    "completion_field": "metadata.completion_status"
                }
            ),
            
            ServiceType.FOLLOWUP_SERVICE.value: ServiceConfig(
                service_name="followup_service",
                collections={
                    "tasks": "task_items",
                    "sets": "task_sets",
                    "media": "media"
                },
                media={
                    "audio_engine": "edgetts",
                    "image_enabled": True,
                    "cache_strategy": "standard",
                    "audio_params": {
                        "language": "ne",
                        "gender": "Female",
                        "length_scale": 1.25,
                        "noise_scale": 0.667,
                        "noise_w": 0.8,
                        "sentence_silence": 0.5
                    }
                },
                processing={
                    "priority_mode": False,
                    "sequential_audio": True,
                    "background_tasks": False,
                    "socketio_enabled": False,
                    "thumbnail_generation": True
                },
                database={
                    "status_field": "metadata.status",
                    "options_audio_field": "metadata.options_audio_status",
                    "completion_field": "metadata.completion_status"
                }
            )
        }
    
    def get_config(self, service_name: str) -> ServiceConfig:
        """
        Get configuration for a specific service.
        
        Args:
            service_name: Name of the service
            
        Returns:
            ServiceConfig: Configuration object for the service
            
        Raises:
            ValueError: If service is not supported
        """
        if service_name not in self._configs:
            raise ValueError(f"Unsupported service: {service_name}")
        
        return self._configs[service_name]
    
    def detect_service_from_context(self, **kwargs) -> str:
        """
        Detect service type from context clues.
        
        Args:
            **kwargs: Context information (collection names, etc.)
            
        Returns:
            str: Detected service name
        """
        # Check for collection hints
        if "curated_content_items" in str(kwargs):
            return ServiceType.EDITOR_SERVICE.value
        elif "followup" in str(kwargs).lower():
            return ServiceType.FOLLOWUP_SERVICE.value
        else:
            return ServiceType.SOCKET_SERVICE_V2.value
    
    def get_collection_name(self, service_name: str, collection_type: str) -> str:
        """
        Get the actual collection name for a service.
        
        Args:
            service_name: Name of the service
            collection_type: Type of collection (tasks, sets, media)
            
        Returns:
            str: Actual collection name
        """
        config = self.get_config(service_name)
        return config.collections.get(collection_type, collection_type)
    
    def get_audio_engine(self, service_name: str) -> str:
        """Get the preferred audio engine for a service."""
        config = self.get_config(service_name)
        return config.media["audio_engine"]
    
    def get_audio_params(self, service_name: str) -> Dict[str, Any]:
        """Get audio generation parameters for a service."""
        config = self.get_config(service_name)
        return config.media["audio_params"]
    
    def is_priority_mode_enabled(self, service_name: str) -> bool:
        """Check if priority mode is enabled for a service."""
        config = self.get_config(service_name)
        return config.processing["priority_mode"]
    
    def is_socketio_enabled(self, service_name: str) -> bool:
        """Check if SocketIO notifications are enabled for a service."""
        config = self.get_config(service_name)
        return config.processing["socketio_enabled"]
