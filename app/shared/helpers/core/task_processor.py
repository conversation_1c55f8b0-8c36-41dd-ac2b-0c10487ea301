"""
Task Processor - Main Orchestrator for Unified Task Processing

This is the main entry point for all task processing operations across services.
It provides a unified interface while handling service-specific requirements
through configuration-driven behavior.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timezone
from bson import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.helpers.core.config_manager import ConfigManager
from app.shared.helpers.media.media_manager import MediaManager
from app.shared.helpers.database.db_manager import DatabaseManager
from app.shared.helpers.database.status_manager import StatusManager

logger = setup_new_logging(__name__)


class TaskProcessor:
    """
    Main orchestrator for task processing across all services.
    
    Provides a unified interface for task processing while handling
    service-specific requirements through configuration.
    """
    
    def __init__(self, service_name: Optional[str] = None):
        """
        Initialize the task processor.
        
        Args:
            service_name: Name of the service (auto-detected if not provided)
        """
        self.config_manager = ConfigManager()
        self.service_name = service_name
        self.media_manager = None
        self.db_manager = None
        self.status_manager = None
    
    async def initialize(self, current_user: UserTenantDB, service_name: Optional[str] = None):
        """
        Initialize the processor with user context and service configuration.
        
        Args:
            current_user: User context for database operations
            service_name: Override service name if needed
        """
        if service_name:
            self.service_name = service_name
        
        if not self.service_name:
            raise ValueError("Service name must be provided or auto-detected")
        
        # Initialize managers with service configuration
        config = self.config_manager.get_config(self.service_name)
        
        self.media_manager = MediaManager(self.service_name, self.config_manager)
        self.db_manager = DatabaseManager(self.service_name, self.config_manager)
        self.status_manager = StatusManager(self.service_name, self.config_manager)
        
        # Initialize with user context
        await self.media_manager.initialize(current_user)
        await self.db_manager.initialize(current_user)
        await self.status_manager.initialize(current_user)
        
        logger.info(f"TaskProcessor initialized for service: {self.service_name}")
    
    async def process_task_media_generation(
        self,
        current_user: UserTenantDB,
        task: Dict[str, Any],
        task_id: ObjectId,
        socketio_server: Optional[Any] = None
    ) -> bool:
        """
        Process media generation for a single task.
        
        Args:
            current_user: User context
            task: Task data
            task_id: Task ID
            socketio_server: Optional SocketIO server for notifications
            
        Returns:
            bool: Success status
        """
        try:
            if not self.media_manager:
                await self.initialize(current_user)
            
            # Generate task media (image/audio)
            success = await self.media_manager.generate_task_media(
                task, task_id, socketio_server
            )
            
            if success:
                # Update completion status
                await self.status_manager.update_task_completion_status(
                    task_id, task.get("type", "unknown")
                )
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to process task media for {task_id}: {e}")
            return False
    
    async def process_options_audio_generation(
        self,
        current_user: UserTenantDB,
        task: Dict[str, Any],
        task_id: ObjectId,
        socketio_server: Optional[Any] = None
    ) -> bool:
        """
        Process options audio generation for a task.
        
        Args:
            current_user: User context
            task: Task data
            task_id: Task ID
            socketio_server: Optional SocketIO server for notifications
            
        Returns:
            bool: Success status
        """
        try:
            if not self.media_manager:
                await self.initialize(current_user)
            
            # Check if task has options
            options = task.get("question", {}).get("options")
            if not options:
                logger.info(f"No options found for task {task_id}")
                return True
            
            # Generate options audio
            success = await self.media_manager.generate_options_audio(
                task, task_id, socketio_server
            )
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to process options audio for {task_id}: {e}")
            return False
    
    async def process_task_set_completion(
        self,
        current_user: UserTenantDB,
        task_set_id: str,
        check_thumbnail: bool = True
    ) -> bool:
        """
        Process task set completion and trigger thumbnail generation if needed.
        
        Args:
            current_user: User context
            task_set_id: Task set ID
            check_thumbnail: Whether to check and generate thumbnail
            
        Returns:
            bool: Success status
        """
        try:
            if not self.status_manager:
                await self.initialize(current_user)
            
            if check_thumbnail:
                # Check if thumbnail generation is needed
                await self.status_manager.check_and_generate_thumbnail(task_set_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to process task set completion for {task_set_id}: {e}")
            return False
    
    async def process_batch_tasks(
        self,
        current_user: UserTenantDB,
        tasks: List[Dict[str, Any]],
        task_ids: List[ObjectId],
        socketio_server: Optional[Any] = None,
        parallel: bool = True
    ) -> Dict[str, Any]:
        """
        Process multiple tasks in batch.
        
        Args:
            current_user: User context
            tasks: List of task data
            task_ids: List of task IDs
            socketio_server: Optional SocketIO server for notifications
            parallel: Whether to process tasks in parallel
            
        Returns:
            Dict[str, Any]: Processing results
        """
        try:
            if not self.media_manager:
                await self.initialize(current_user)
            
            results = {
                "total": len(tasks),
                "successful": 0,
                "failed": 0,
                "errors": []
            }
            
            if parallel and len(tasks) > 1:
                # Process tasks in parallel
                tasks_coroutines = []
                for task, task_id in zip(tasks, task_ids):
                    coro = self._process_single_task(
                        current_user, task, task_id, socketio_server
                    )
                    tasks_coroutines.append(coro)
                
                task_results = await asyncio.gather(*tasks_coroutines, return_exceptions=True)
                
                for i, result in enumerate(task_results):
                    if isinstance(result, Exception):
                        results["failed"] += 1
                        results["errors"].append(f"Task {task_ids[i]}: {str(result)}")
                    elif result:
                        results["successful"] += 1
                    else:
                        results["failed"] += 1
            else:
                # Process tasks sequentially
                for task, task_id in zip(tasks, task_ids):
                    try:
                        success = await self._process_single_task(
                            current_user, task, task_id, socketio_server
                        )
                        if success:
                            results["successful"] += 1
                        else:
                            results["failed"] += 1
                    except Exception as e:
                        results["failed"] += 1
                        results["errors"].append(f"Task {task_id}: {str(e)}")
            
            logger.info(f"Batch processing completed: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Failed to process batch tasks: {e}")
            return {"total": len(tasks), "successful": 0, "failed": len(tasks), "errors": [str(e)]}
    
    async def _process_single_task(
        self,
        current_user: UserTenantDB,
        task: Dict[str, Any],
        task_id: ObjectId,
        socketio_server: Optional[Any] = None
    ) -> bool:
        """Process a single task with media and options audio generation."""
        try:
            # Generate task media
            media_success = await self.process_task_media_generation(
                current_user, task, task_id, socketio_server
            )
            
            # Generate options audio if needed
            options_success = await self.process_options_audio_generation(
                current_user, task, task_id, socketio_server
            )
            
            return media_success and options_success
            
        except Exception as e:
            logger.error(f"Failed to process single task {task_id}: {e}")
            return False
