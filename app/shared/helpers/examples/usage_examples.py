"""
Usage Examples for the Unified Helper System

This file demonstrates how to use the unified helper system across
different services to replace duplicate functionality.
"""

from typing import Dict, Any, List
from bson import ObjectId

from app.shared.helpers import TaskProcessor
from app.shared.models.user import UserTenantDB


async def socket_service_example(current_user: UserTenantDB, task_data: Dict[str, Any], task_id: ObjectId):
    """
    Example: Using unified helpers in Socket Service V2
    
    Replaces the duplicate functions in task_utils_v2.py
    """
    # Initialize task processor for socket service
    processor = TaskProcessor("socket_service_v2")
    await processor.initialize(current_user)
    
    # Generate task media (replaces _generate_task_media)
    media_success = await processor.process_task_media_generation(
        current_user, task_data, task_id, socketio_server=None
    )
    
    # Generate options audio (replaces _generate_options_audio_sequential)
    options_success = await processor.process_options_audio_generation(
        current_user, task_data, task_id, socketio_server=None
    )
    
    # Check and generate thumbnail (replaces _check_and_generate_thumbnail)
    if media_success and options_success:
        await processor.process_task_set_completion(
            current_user, str(task_data.get("collection_id", "")), check_thumbnail=True
        )
    
    return media_success and options_success


async def editor_service_example(current_user: UserTenantDB, task_items: List[Dict[str, Any]], task_ids: List[ObjectId]):
    """
    Example: Using unified helpers in Editor Service
    
    Replaces the duplicate functions in split_and_manage.py
    """
    # Initialize task processor for editor service
    processor = TaskProcessor("editor_service")
    await processor.initialize(current_user)
    
    # Process multiple tasks in batch (replaces generate_media_for_tasks_and_stories)
    results = await processor.process_batch_tasks(
        current_user, task_items, task_ids, socketio_server=None, parallel=True
    )
    
    print(f"Editor service batch processing: {results['successful']}/{results['total']} successful")
    return results


async def followup_service_example(current_user: UserTenantDB, followup_tasks: List[Dict[str, Any]], task_ids: List[ObjectId]):
    """
    Example: Using unified helpers in Followup Service
    
    Replaces the imports from task_utils_v2.py in followup_simple.py
    """
    # Initialize task processor for followup service
    processor = TaskProcessor("followup_service")
    await processor.initialize(current_user)
    
    # Process followup tasks sequentially (followup typically doesn't use parallel processing)
    results = await processor.process_batch_tasks(
        current_user, followup_tasks, task_ids, socketio_server=None, parallel=False
    )
    
    print(f"Followup service processing: {results['successful']}/{results['total']} successful")
    return results


async def direct_media_generation_example(current_user: UserTenantDB, service_name: str):
    """
    Example: Direct media generation using individual components
    
    For cases where you need more control over the media generation process
    """
    from app.shared.helpers import MediaManager, ConfigManager
    
    # Initialize components
    config_manager = ConfigManager()
    media_manager = MediaManager(service_name, config_manager)
    await media_manager.initialize(current_user)
    
    # Generate audio directly
    audio_url, file_info, usage_metadata = await media_manager.audio_generator.generate_audio(
        "नमस्ते", "greeting_audio", cache_enabled=True
    )
    
    if audio_url:
        print(f"Generated audio: {audio_url}")
    
    # Generate image directly
    image_url, file_info, usage_metadata = await media_manager.image_generator.generate_image(
        "beautiful mountain landscape", "landscape_image", cache_enabled=True
    )
    
    if image_url:
        print(f"Generated image: {image_url}")
    
    return audio_url, image_url


async def cache_management_example(current_user: UserTenantDB, service_name: str):
    """
    Example: Cache management operations
    
    Demonstrates how to work with the unified cache system
    """
    from app.shared.helpers.media import CacheManager
    from app.shared.helpers import ConfigManager
    
    # Initialize cache manager
    config_manager = ConfigManager()
    cache_manager = CacheManager(service_name, config_manager)
    await cache_manager.initialize(current_user)
    
    # Check cache
    cached_audio = await cache_manager.check_media_cache("hello", "audio", "greeting")
    if cached_audio:
        print(f"Found cached audio: {cached_audio['file_text']}")
    
    # Get cache statistics
    stats = await cache_manager.get_cache_stats()
    print(f"Cache stats: {stats}")
    
    # Invalidate cache if needed
    await cache_manager.invalidate_cache("old_keyword", "audio")
    
    return stats


async def database_operations_example(current_user: UserTenantDB, service_name: str, task_id: ObjectId):
    """
    Example: Database operations using unified database manager
    
    Demonstrates consistent database operations across services
    """
    from app.shared.helpers import DatabaseManager, ConfigManager
    
    # Initialize database manager
    config_manager = ConfigManager()
    db_manager = DatabaseManager(service_name, config_manager)
    await db_manager.initialize(current_user)
    
    # Update task status
    success = await db_manager.update_task_status(task_id, "completed", {
        "completion_reason": "media_generated",
        "processed_by": "unified_helper_system"
    })
    
    # Get task information
    task = await db_manager.get_task(task_id)
    if task:
        print(f"Task status: {task.get('metadata', {}).get('status')}")
    
    # Get collection statistics
    stats = await db_manager.get_collection_stats()
    print(f"Database stats: {stats}")
    
    return success


async def status_tracking_example(current_user: UserTenantDB, service_name: str, task_set_id: str):
    """
    Example: Status tracking and completion management
    
    Demonstrates centralized status management across services
    """
    from app.shared.helpers import StatusManager, ConfigManager
    
    # Initialize status manager
    config_manager = ConfigManager()
    status_manager = StatusManager(service_name, config_manager)
    await status_manager.initialize(current_user)
    
    # Check and generate thumbnail
    thumbnail_success = await status_manager.check_and_generate_thumbnail(task_set_id)
    
    # Get completion statistics
    completion_stats = await status_manager.get_completion_stats(task_set_id)
    print(f"Completion stats: {completion_stats}")
    
    # Mark followup level completed (if applicable)
    if service_name == "followup_service":
        await status_manager.mark_followup_level_completed(task_set_id, 1)
    
    return thumbnail_success, completion_stats


# Migration helper functions for existing services

async def migrate_socket_service_function(current_user: UserTenantDB, task: Dict[str, Any], task_id: ObjectId):
    """
    Migration helper: Replace socket service task_utils_v2 functions
    
    This function shows how to replace existing socket service functions
    with the unified helper system.
    """
    # OLD WAY (task_utils_v2.py):
    # await _generate_task_media(current_user, task, task_id, socketio_server)
    # await _generate_options_audio_sequential(current_user, task, task_id, socketio_server)
    # await _update_task_generation_completion_status(current_user, task_id, task_type)
    
    # NEW WAY (unified helpers):
    processor = TaskProcessor("socket_service_v2")
    await processor.initialize(current_user)
    
    # Single call replaces all the above functions
    success = await processor._process_single_task(current_user, task, task_id, None)
    
    return success


async def migrate_editor_service_function(current_user: UserTenantDB, task_items: List[Dict[str, Any]], task_ids: List[ObjectId]):
    """
    Migration helper: Replace editor service split_and_manage functions
    
    This function shows how to replace existing editor service functions
    with the unified helper system.
    """
    # OLD WAY (split_and_manage.py):
    # await generate_media_for_tasks_and_stories(current_user, task_item_ids, story_item_ids, generate_image, generate_audio_task)
    # await _generate_curated_options_audio(current_user, task_item, task_id, generate_audio_task_func)
    
    # NEW WAY (unified helpers):
    processor = TaskProcessor("editor_service")
    await processor.initialize(current_user)
    
    # Single call replaces the complex media generation logic
    results = await processor.process_batch_tasks(current_user, task_items, task_ids, parallel=True)
    
    return results


async def migrate_followup_service_function(current_user: UserTenantDB, task: Dict[str, Any], task_id: ObjectId):
    """
    Migration helper: Replace followup service imports from task_utils_v2
    
    This function shows how to replace followup service imports
    with the unified helper system.
    """
    # OLD WAY (followup_simple.py):
    # from app.v2.api.socket_service_v2.generator.task_utils_v2 import _generate_task_media, _generate_options_audio_sequential
    # await _generate_task_media(current_user, task, task_id, None)
    # await _generate_options_audio_sequential(current_user, task, task_id, None)
    
    # NEW WAY (unified helpers):
    processor = TaskProcessor("followup_service")
    await processor.initialize(current_user)
    
    # Use the same interface as other services
    success = await processor._process_single_task(current_user, task, task_id, None)
    
    return success
